// test_debug.go - Comprehensive test to evaluate RAG performance with debug mode
package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"
	"github.com/denissud/rag-pipeline-go/internal/rag"
)

type TestResult struct {
	Query            string
	Answer           string
	Debug            *rag.RAGQueryDebugInfo
	ReferencedDocs   map[string]string // source -> content
	Timestamp        time.Time
}

type TestSuite struct {
	Results          []TestResult
	TotalTime        time.Duration
	AverageQueryTime time.Duration
	DocumentSources  map[string]string // All available documents
}

func loadDocuments(docsDir string) (map[string]string, error) {
	documents := make(map[string]string)

	files, err := os.ReadDir(docsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %w", docsDir, err)
	}

	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".md" {
			filePath := filepath.Join(docsDir, file.Name())
			content, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("Failed to read file %s: %v", filePath, err)
				continue
			}
			documents[filePath] = string(content)
		}
	}

	return documents, nil
}

func ingestAllDocuments(ctx context.Context, ragPipeline *rag.RAGPipeline, documents map[string]string) error {
	fmt.Println("=== Ingesting Documents ===")

	for source, content := range documents {
		fmt.Printf("Ingesting: %s\n", source)
		err := ragPipeline.IngestDocument(ctx, content, source)
		if err != nil {
			return fmt.Errorf("failed to ingest %s: %w", source, err)
		}
	}

	fmt.Printf("Successfully ingested %d documents\n", len(documents))
	return nil
}

func runTestQueries(ctx context.Context, ragPipeline *rag.RAGPipeline, documents map[string]string) (*TestSuite, error) {
	// Define test queries that should be answerable from the documents
	testQueries := []string{
		"What is a RAG pipeline?",
		"What is Qwen3-Embedding-0.6B?",
		"How do you use Go with Nix?",
		"What is Qdrant?",
		"What are the benefits of using Go for building applications?",
		"What is the purpose of vector databases?",
		"How does semantic search work?",
		"What models are included in the Qwen series?",
		"What is the difference between base and instruction-tuned models?",
		"How do embeddings capture semantic meaning?",
	}

	suite := &TestSuite{
		Results:         make([]TestResult, 0, len(testQueries)),
		DocumentSources: documents,
	}

	startTime := time.Now()
	var totalQueryTime time.Duration

	fmt.Println("\n=== Running Test Queries ===")

	for i, query := range testQueries {
		fmt.Printf("\n[%d/%d] Processing query: %s\n", i+1, len(testQueries), query)

		queryCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)

		result, err := ragPipeline.RAGQueryWithDebug(queryCtx, query)
		cancel()

		if err != nil {
			log.Printf("Error processing query '%s': %v", query, err)
			continue
		}

		// Extract referenced documents
		referencedDocs := make(map[string]string)
		if result.Debug != nil {
			for _, source := range result.Debug.RetrievedSources {
				if content, exists := documents[source]; exists {
					referencedDocs[source] = content
				}
			}
			totalQueryTime += result.Debug.TotalTime
		}

		testResult := TestResult{
			Query:          query,
			Answer:         result.Answer,
			Debug:          result.Debug,
			ReferencedDocs: referencedDocs,
			Timestamp:      time.Now(),
		}

		suite.Results = append(suite.Results, testResult)

		fmt.Printf("Answer: %s\n", result.Answer)
		if result.Debug != nil {
			fmt.Printf("Processing time: %v\n", result.Debug.TotalTime)
			fmt.Printf("Referenced docs: %v\n", result.Debug.RetrievedSources)
		}
	}

	suite.TotalTime = time.Since(startTime)
	if len(suite.Results) > 0 {
		suite.AverageQueryTime = totalQueryTime / time.Duration(len(suite.Results))
	}

	return suite, nil
}

func generateMarkdownReport(suite *TestSuite, outputFile string) error {
	var report strings.Builder

	// Header
	report.WriteString("# RAG Pipeline Performance Report\n\n")
	report.WriteString(fmt.Sprintf("**Generated:** %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	// Summary
	report.WriteString("## Summary\n\n")
	report.WriteString(fmt.Sprintf("- **Total Queries:** %d\n", len(suite.Results)))
	report.WriteString(fmt.Sprintf("- **Total Test Time:** %v\n", suite.TotalTime))
	report.WriteString(fmt.Sprintf("- **Average Query Time:** %v\n", suite.AverageQueryTime))
	report.WriteString(fmt.Sprintf("- **Available Documents:** %d\n\n", len(suite.DocumentSources)))

	// Performance Metrics
	report.WriteString("## Performance Metrics\n\n")
	report.WriteString("| Query | Total Time | Embedding Time | Retrieval Time | Generation Time | Docs Retrieved |\n")
	report.WriteString("|-------|------------|----------------|----------------|-----------------|----------------|\n")

	for i, result := range suite.Results {
		if result.Debug != nil {
			report.WriteString(fmt.Sprintf("| Q%d | %v | %v | %v | %v | %d |\n",
				i+1,
				result.Debug.TotalTime,
				result.Debug.EmbeddingTime,
				result.Debug.RetrievalTime,
				result.Debug.GenerationTime,
				result.Debug.RetrievedDocsCount,
			))
		}
	}
	report.WriteString("\n")

	// Detailed Results
	report.WriteString("## Detailed Query Results\n\n")

	for i, result := range suite.Results {
		report.WriteString(fmt.Sprintf("### Query %d\n\n", i+1))
		report.WriteString(fmt.Sprintf("**Question:** %s\n\n", result.Query))
		report.WriteString(fmt.Sprintf("**Answer:** %s\n\n", result.Answer))

		if result.Debug != nil {
			// Performance details
			report.WriteString("**Performance Details:**\n")
			report.WriteString(fmt.Sprintf("- Total processing time: %v\n", result.Debug.TotalTime))
			report.WriteString(fmt.Sprintf("- Embedding generation: %v\n", result.Debug.EmbeddingTime))
			report.WriteString(fmt.Sprintf("- Document retrieval: %v\n", result.Debug.RetrievalTime))
			report.WriteString(fmt.Sprintf("- Answer generation: %v\n", result.Debug.GenerationTime))
			report.WriteString(fmt.Sprintf("- Documents retrieved: %d\n", result.Debug.RetrievedDocsCount))
			report.WriteString(fmt.Sprintf("- Context length: %d characters\n", result.Debug.ContextLength))
			report.WriteString(fmt.Sprintf("- Response length: %d characters\n\n", result.Debug.ResponseLength))

			// Retrieved documents with scores
			if len(result.Debug.RetrievedSources) > 0 {
				report.WriteString("**Retrieved Documents:**\n")
				for j, source := range result.Debug.RetrievedSources {
					score := "N/A"
					if j < len(result.Debug.RetrievedScores) {
						score = fmt.Sprintf("%.3f", result.Debug.RetrievedScores[j])
					}
					report.WriteString(fmt.Sprintf("- `%s` (Score: %s)\n", source, score))
				}
				report.WriteString("\n")
			}
		}

		// Referenced document content
		if len(result.ReferencedDocs) > 0 {
			report.WriteString("**Referenced Document Content:**\n\n")
			for source, content := range result.ReferencedDocs {
				report.WriteString(fmt.Sprintf("#### Source: `%s`\n\n", source))
				report.WriteString("```markdown\n")
				// Truncate very long content
				if len(content) > 2000 {
					report.WriteString(content[:2000] + "\n... (content truncated)")
				} else {
					report.WriteString(content)
				}
				report.WriteString("\n```\n\n")
			}
		}

		report.WriteString("---\n\n")
	}

	// Document Analysis
	report.WriteString("## Document Analysis\n\n")
	report.WriteString("### Available Documents\n\n")
	for source, content := range suite.DocumentSources {
		report.WriteString(fmt.Sprintf("#### `%s`\n", source))
		report.WriteString(fmt.Sprintf("- **Length:** %d characters\n", len(content)))

		// Count how many times this document was referenced
		refCount := 0
		for _, result := range suite.Results {
			if _, referenced := result.ReferencedDocs[source]; referenced {
				refCount++
			}
		}
		report.WriteString(fmt.Sprintf("- **Referenced in queries:** %d/%d (%.1f%%)\n\n",
			refCount, len(suite.Results), float64(refCount)/float64(len(suite.Results))*100))
	}

	// Write to file
	return os.WriteFile(outputFile, []byte(report.String()), 0644)
}

func main() {
	fmt.Println("=== RAG Pipeline Performance Test ===")

	// Create config with debug mode enabled
	cfg := &config.Config{
		OllamaAPIBaseURL:     "http://localhost:11434",
		OllamaEmbeddingModel: "qwen-embedding",
		OllamaGenerateModel:  "qwen3:1.7b",
		QdrantGRPCURL:        "localhost:6334",
		QdrantCollectionName: "rag_pipeline_performance_test",
		VectorSize:           1024,
		DebugMode:            true,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// Load all documents
	fmt.Println("Loading documents...")
	documents, err := loadDocuments("./docs")
	if err != nil {
		log.Fatalf("Failed to load documents: %v", err)
	}
	fmt.Printf("Loaded %d documents\n", len(documents))

	// Initialize clients
	fmt.Println("Initializing clients...")
	ollamaClient := ollama.NewClient(cfg)
	qdrantClient, err := qdrant.NewClient(ctx, cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}

	// Initialize RAG pipeline
	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,
		1024, // Chunk Size
		128,  // Chunk Overlap
		5,    // Top K for retrieval
	)

	// Ingest all documents
	err = ingestAllDocuments(ctx, ragPipeline, documents)
	if err != nil {
		log.Fatalf("Failed to ingest documents: %v", err)
	}

	// Run test queries
	suite, err := runTestQueries(ctx, ragPipeline, documents)
	if err != nil {
		log.Fatalf("Failed to run test queries: %v", err)
	}

	// Generate report
	outputFile := fmt.Sprintf("rag_performance_report_%s.md", time.Now().Format("2006-01-02_15-04-05"))
	fmt.Printf("\nGenerating report: %s\n", outputFile)

	err = generateMarkdownReport(suite, outputFile)
	if err != nil {
		log.Fatalf("Failed to generate report: %v", err)
	}

	fmt.Printf("\n=== Test Complete ===\n")
	fmt.Printf("Report saved to: %s\n", outputFile)
	fmt.Printf("Total queries processed: %d\n", len(suite.Results))
	fmt.Printf("Total test time: %v\n", suite.TotalTime)
	fmt.Printf("Average query time: %v\n", suite.AverageQueryTime)
}
