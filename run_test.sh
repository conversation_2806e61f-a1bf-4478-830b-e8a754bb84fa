#!/bin/bash

# run_test.sh - <PERSON>ript to run RAG pipeline performance tests

echo "=== RAG Pipeline Performance Test Runner ==="
echo

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    exit 1
fi

# Check if docs directory exists
if [ ! -d "./docs" ]; then
    echo "Error: ./docs directory not found"
    echo "Please ensure you have markdown documents in the ./docs directory"
    exit 1
fi

# Count documents
doc_count=$(find ./docs -name "*.md" | wc -l)
echo "Found $doc_count markdown documents in ./docs"

if [ $doc_count -eq 0 ]; then
    echo "Warning: No markdown documents found in ./docs"
    echo "The test will still run but may not produce meaningful results"
fi

echo

# Set debug mode
export DEBUG_MODE=true

echo "Starting test with debug mode enabled..."
echo "This may take several minutes depending on your system and the number of documents."
echo

# Run the test
if go run test.go; then
    echo
    echo "=== Test completed successfully! ==="
    echo
    echo "Generated files:"
    ls -la test_results_*.md 2>/dev/null | tail -5
    echo
    echo "You can view the latest report with:"
    latest_report=$(ls -t test_results_*.md 2>/dev/null | head -1)
    if [ -n "$latest_report" ]; then
        echo "  cat '$latest_report'"
        echo "  or open '$latest_report' in your markdown viewer"
    fi
else
    echo
    echo "=== Test failed! ==="
    echo "Please check the error messages above and ensure:"
    echo "1. Ollama is running (http://localhost:11434)"
    echo "2. Qdrant is running (localhost:6334)"
    echo "3. Required models are available"
    echo "4. Network connectivity is working"
    exit 1
fi
