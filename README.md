# RAG Pipeline in Go

A high-performance Retrieval-Augmented Generation (RAG) pipeline built in Go, featuring comprehensive debug capabilities and a user-friendly CLI interface.

## Features

- **🚀 High Performance**: Built in Go for speed and efficiency
- **🔍 Vector Search**: Powered by Qdrant vector database
- **🤖 Local LLMs**: Integration with Ollama for embeddings and generation
- **🐛 Debug Mode**: Comprehensive debugging and performance analysis
- **💬 Interactive Chat**: Real-time question-answering interface
- **📊 Performance Testing**: Built-in evaluation and benchmarking tools
- **⚙️ Configurable**: Flexible configuration via environment variables and CLI flags

## Quick Start

### Prerequisites

1. **Ollama** - For running local language models
   ```bash
   # Install Ollama (see https://ollama.ai)
   ollama serve
   
   # Pull required models
   ollama pull qwen-embedding
   ollama pull qwen3:1.7b
   ```

2. **Qdrant** - Vector database
   ```bash
   # Using Docker
   docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant
   ```

3. **Go** - Version 1.19 or later

### Installation

```bash
git clone <repository-url>
cd secure-ai
go build -o rag-cli .
```

### Basic Usage

1. **Check system status**
   ```bash
   ./rag-cli status
   ```

2. **Ingest documents**
   ```bash
   ./rag-cli ingest ./docs
   ```

3. **Ask questions**
   ```bash
   ./rag-cli query "What is a RAG pipeline?"
   ```

4. **Start interactive chat**
   ```bash
   ./rag-cli chat
   ```

5. **Run performance tests**
   ```bash
   ./rag-cli test
   ```

## CLI Commands

### `ingest <directory>`
Ingest markdown documents from a directory into the vector database.

```bash
./rag-cli ingest ./docs
./rag-cli ingest /path/to/documents -debug
```

### `query <question>`
Ask a single question and get an answer.

```bash
./rag-cli query "What is Qdrant?"
./rag-cli query "How does Go work with Nix?" -debug
```

### `chat`
Start an interactive chat session.

```bash
./rag-cli chat
./rag-cli chat -quiet
```

### `test`
Run performance tests with predefined queries.

```bash
./rag-cli test
./rag-cli test -debug
```

### `status`
Check the status of all system components.

```bash
./rag-cli status
```

## CLI Options

| Flag | Description | Default |
|------|-------------|---------|
| `-debug` | Enable debug mode | false |
| `-quiet` | Suppress non-essential output | false |
| `-chunk-size` | Document chunk size | 1024 |
| `-overlap` | Chunk overlap size | 128 |
| `-top-k` | Number of documents to retrieve | 5 |
| `-timeout` | Operation timeout | 5m |

## Configuration

Configure the system using environment variables:

```bash
# Ollama configuration
export OLLAMA_API_BASE_URL="http://localhost:11434"
export OLLAMA_EMBEDDING_MODEL="qwen-embedding"
export OLLAMA_GENERATE_MODEL="qwen3:1.7b"

# Qdrant configuration
export QDRANT_GRPC_URL="localhost:6334"
export QDRANT_COLLECTION_NAME="rag_pipeline_go"
export VECTOR_SIZE="1024"

# Debug mode
export DEBUG_MODE="true"
```

## Debug Mode

Debug mode provides detailed insights into the RAG pipeline's operation:

### Enable Debug Mode
```bash
# Via environment variable
export DEBUG_MODE=true
./rag-cli query "What is RAG?"

# Via CLI flag
./rag-cli query "What is RAG?" -debug
```

### Debug Information Includes
- **Performance Metrics**: Timing for each pipeline stage
- **Document Retrieval**: Which documents were found and their relevance scores
- **Context Analysis**: What content was used to generate the answer
- **Model Details**: Which models were used and their parameters

### Example Debug Output
```json
{
  "query": "What is RAG?",
  "total_time": "2.3s",
  "embedding_time": "150ms",
  "retrieval_time": "45ms", 
  "generation_time": "2.1s",
  "retrieved_docs_count": 3,
  "retrieved_sources": ["./docs/rag_overview.md"],
  "context_length": 1024,
  "response_length": 156
}
```

## Performance Testing

The built-in test suite evaluates RAG performance:

```bash
# Run basic performance test
./rag-cli test

# Generate detailed test report
go run test.go
```

Test reports include:
- Query processing times
- Document retrieval accuracy
- Answer quality analysis
- Source document usage statistics

## Project Structure

```
├── main.go              # CLI interface
├── config/              # Configuration management
├── internal/
│   ├── ollama/         # Ollama client with debug support
│   ├── qdrant/         # Qdrant client with debug support
│   └── rag/            # RAG pipeline implementation
├── docs/               # Sample documents
├── test.go             # Performance testing
└── README.md           # This file
```

## Advanced Usage

### Custom Pipeline Parameters
```bash
./rag-cli query "Question?" \
  -chunk-size 512 \
  -overlap 64 \
  -top-k 10 \
  -debug
```

### Batch Processing
```bash
# Ingest multiple directories
./rag-cli ingest ./docs1
./rag-cli ingest ./docs2
./rag-cli ingest ./docs3

# Test with custom timeout
./rag-cli test -timeout 10m
```

### Performance Optimization
- **Chunk Size**: Smaller chunks (512) for precise retrieval, larger (2048) for context
- **Top-K**: Higher values (10+) for comprehensive context, lower (3-5) for speed
- **Overlap**: Higher overlap (256) for better continuity, lower (64) for speed

## Troubleshooting

### Common Issues

**"Connection refused"**
- Ensure Ollama is running: `ollama serve`
- Check Qdrant is accessible: `docker ps`

**"Model not found"**
- Pull required models: `ollama pull qwen-embedding`

**"No documents found"**
- Verify documents exist: `ls ./docs/*.md`
- Check file permissions

**Slow performance**
- Reduce chunk size: `-chunk-size 512`
- Lower top-k: `-top-k 3`
- Use smaller models

### Debug Commands
```bash
# Check system status
./rag-cli status

# Test with verbose output
./rag-cli test -debug

# Monitor performance
./rag-cli query "test" -debug | grep "total_time"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license here]

## API Reference

### Internal Packages

#### `internal/ollama`
Ollama client with debug capabilities:
- `GetEmbedding(ctx, text)` - Generate embeddings
- `GetEmbeddingWithDebug(ctx, text)` - Generate embeddings with debug info
- `GenerateCompletion(ctx, context, query)` - Generate answers
- `GenerateCompletionWithDebug(ctx, context, query)` - Generate with debug info

#### `internal/qdrant`
Qdrant client with debug capabilities:
- `Search(ctx, embedding, limit)` - Vector similarity search
- `SearchWithDebug(ctx, embedding, limit)` - Search with debug info
- `UpsertPoints(ctx, chunks, embeddings)` - Store document chunks
- `UpsertPointsWithDebug(ctx, chunks, embeddings)` - Store with debug info

#### `internal/rag`
RAG pipeline orchestration:
- `IngestDocument(ctx, content, source)` - Ingest a document
- `IngestDocumentWithDebug(ctx, content, source)` - Ingest with debug info
- `RAGQuery(ctx, query)` - Process a query
- `RAGQueryWithDebug(ctx, query)` - Process with debug info

## Examples

### Programmatic Usage

```go
package main

import (
    "context"
    "fmt"
    "log"

    "github.com/denissud/rag-pipeline-go/config"
    "github.com/denissud/rag-pipeline-go/internal/ollama"
    "github.com/denissud/rag-pipeline-go/internal/qdrant"
    "github.com/denissud/rag-pipeline-go/internal/rag"
)

func main() {
    cfg := config.Load()
    cfg.DebugMode = true

    ctx := context.Background()

    // Initialize clients
    ollamaClient := ollama.NewClient(cfg)
    qdrantClient, err := qdrant.NewClient(ctx, cfg)
    if err != nil {
        log.Fatal(err)
    }

    // Create RAG pipeline
    ragPipeline := rag.NewRAGPipeline(
        ollamaClient, qdrantClient, cfg,
        1024, 128, 5, // chunk size, overlap, top-k
    )

    // Ingest a document
    content := "RAG combines retrieval and generation..."
    err = ragPipeline.IngestDocument(ctx, content, "doc.md")
    if err != nil {
        log.Fatal(err)
    }

    // Query with debug info
    result, err := ragPipeline.RAGQueryWithDebug(ctx, "What is RAG?")
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("Answer: %s\n", result.Answer)
    if result.Debug != nil {
        fmt.Printf("Processing time: %v\n", result.Debug.TotalTime)
        fmt.Printf("Sources: %v\n", result.Debug.RetrievedSources)
    }
}
```

### Custom Configuration

```go
cfg := &config.Config{
    OllamaAPIBaseURL:     "http://localhost:11434",
    OllamaEmbeddingModel: "custom-embedding-model",
    OllamaGenerateModel:  "custom-generation-model",
    QdrantGRPCURL:        "localhost:6334",
    QdrantCollectionName: "my_collection",
    VectorSize:           1024,
    DebugMode:            true,
}
```

## Performance Benchmarks

Typical performance on modern hardware:

| Operation | Time | Notes |
|-----------|------|-------|
| Document Ingestion | 100-500ms | Per 1KB document |
| Embedding Generation | 50-200ms | Per query |
| Vector Search | 10-50ms | Per query |
| Answer Generation | 1-5s | Depends on model size |
| End-to-end Query | 1-6s | Complete pipeline |

## Deployment

### Docker Deployment

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o rag-cli .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/rag-cli .
COPY --from=builder /app/docs ./docs
CMD ["./rag-cli", "status"]
```

### Production Configuration

```bash
# Production environment variables
export OLLAMA_API_BASE_URL="http://ollama-service:11434"
export QDRANT_GRPC_URL="qdrant-service:6334"
export QDRANT_COLLECTION_NAME="production_rag"
export DEBUG_MODE="false"
export VECTOR_SIZE="1024"
```

## Monitoring

### Health Checks

```bash
# Basic health check
./rag-cli status

# Detailed system check
./rag-cli test -timeout 30s
```

### Metrics Collection

Debug mode provides metrics for monitoring:
- Query processing times
- Document retrieval accuracy
- Model performance
- Error rates

## Security Considerations

- **Local Processing**: All data stays on your infrastructure
- **No External APIs**: Uses local Ollama and Qdrant instances
- **Configurable Access**: Control API endpoints and ports
- **Data Isolation**: Separate collections for different use cases

## Acknowledgments

- [Ollama](https://ollama.ai) for local LLM support
- [Qdrant](https://qdrant.tech) for vector database
- [Qwen](https://github.com/QwenLM/Qwen) for embedding and generation models
