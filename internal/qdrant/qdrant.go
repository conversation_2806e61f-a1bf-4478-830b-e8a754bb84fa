package qdrant

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/denissud/rag-pipeline-go/config"

	"github.com/qdrant/go-client/qdrant"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// DocumentChunk represents a piece of text to be stored in Qdrant
type DocumentChunk struct {
	ID      string
	Content string
	Source  string
}

// UpsertDebugInfo contains debug information for upsert operations
type UpsertDebugInfo struct {
	CollectionName string        `json:"collection_name"`
	NumPoints      int           `json:"num_points"`
	VectorSize     uint64        `json:"vector_size"`
	RequestTime    time.Duration `json:"request_time"`
	ChunkSources   []string      `json:"chunk_sources"`
}

// UpsertWithDebug contains debug information for upsert operations
type UpsertWithDebug struct {
	Debug *UpsertDebugInfo `json:"debug,omitempty"`
}

// SearchDebugInfo contains debug information for search operations
type SearchDebugInfo struct {
	CollectionName   string        `json:"collection_name"`
	QueryVectorSize  int           `json:"query_vector_size"`
	Limit            uint64        `json:"limit"`
	RequestTime      time.Duration `json:"request_time"`
	ResultCount      int           `json:"result_count"`
	TopScore         float32       `json:"top_score,omitempty"`
	LowestScore      float32       `json:"lowest_score,omitempty"`
	RetrievedSources []string      `json:"retrieved_sources"`
}

// SearchWithDebug contains both search results and debug information
type SearchWithDebug struct {
	Results []*qdrant.ScoredPoint `json:"results"`
	Debug   *SearchDebugInfo      `json:"debug,omitempty"`
}

// Client manages interactions with the Qdrant database
type Client struct {
	qdrantClient     qdrant.PointsClient
	collectionName   string
	vectorSize       uint64
	debugMode        bool
}

// NewClient creates a new Qdrant client and ensures the collection exists
func NewClient(ctx context.Context, cfg *config.Config) (*Client, error) {
	conn, err := grpc.Dial(cfg.QdrantGRPCURL, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("could not connect to Qdrant: %w", err)
	}

	collectionsClient := qdrant.NewCollectionsClient(conn)
	
	// Check if collection exists
	res, err := collectionsClient.Get(ctx, &qdrant.GetCollectionInfoRequest{
		CollectionName: cfg.QdrantCollectionName,
	})
	
	if err != nil { // A gRPC error could mean the collection does not exist
		log.Printf("Collection '%s' may not exist, attempting to create it. Error: %v", cfg.QdrantCollectionName, err)
		
		_, createErr := collectionsClient.Create(ctx, &qdrant.CreateCollection{
			CollectionName: cfg.QdrantCollectionName,
			VectorsConfig: &qdrant.VectorsConfig{
				Config: &qdrant.VectorsConfig_Params{
					Params: &qdrant.VectorParams{
						Size:     cfg.VectorSize,
						Distance: qdrant.Distance_Cosine,
					},
				},
			},
		})
		if createErr != nil {
			return nil, fmt.Errorf("could not create collection '%s': %w", cfg.QdrantCollectionName, createErr)
		}
		log.Printf("Successfully created collection '%s'", cfg.QdrantCollectionName)
	} else {
		log.Printf("Collection '%s' already exists. Details: %v", cfg.QdrantCollectionName, res.GetResult())
	}


	return &Client{
		qdrantClient:     qdrant.NewPointsClient(conn),
		collectionName:   cfg.QdrantCollectionName,
		vectorSize:       cfg.VectorSize,
		debugMode:        cfg.DebugMode,
	}, nil
}

// UpsertPoints inserts or updates document chunks and their embeddings
func (c *Client) UpsertPoints(ctx context.Context, chunks []DocumentChunk, embeddings [][]float32) error {
	result, err := c.UpsertPointsWithDebug(ctx, chunks, embeddings)
	if err != nil {
		return err
	}
	_ = result // Ignore debug result in non-debug mode
	return nil
}

// UpsertPointsWithDebug inserts or updates document chunks and their embeddings with debug information
func (c *Client) UpsertPointsWithDebug(ctx context.Context, chunks []DocumentChunk, embeddings [][]float32) (*UpsertWithDebug, error) {
	startTime := time.Now()
	if len(chunks) != len(embeddings) {
		return nil, fmt.Errorf("number of chunks (%d) and embeddings (%d) must be equal", len(chunks), len(embeddings))
	}

	points := make([]*qdrant.PointStruct, len(chunks))
	chunkSources := make([]string, len(chunks))
	for i, chunk := range chunks {
		chunkSources[i] = chunk.Source
		points[i] = &qdrant.PointStruct{
			Id: &qdrant.PointId{
				PointIdOptions: &qdrant.PointId_Uuid{
					Uuid: chunk.ID,
				},
			},
			Payload: map[string]*qdrant.Value{
				"content": {
					Kind: &qdrant.Value_StringValue{StringValue: chunk.Content},
				},
				"source": {
					Kind: &qdrant.Value_StringValue{StringValue: chunk.Source},
				},
			},
			Vectors: &qdrant.Vectors{
				VectorsOptions: &qdrant.Vectors_Vector{
					Vector: &qdrant.Vector{
						Data: embeddings[i],
					},
				},
			},
		}
	}

	waitUpsert := true
	_, err := c.qdrantClient.Upsert(ctx, &qdrant.UpsertPoints{
		CollectionName: c.collectionName,
		Points:         points,
		Wait:           &waitUpsert,
	})

	if err != nil {
		return nil, fmt.Errorf("could not upsert points: %w", err)
	}

	requestTime := time.Since(startTime)

	result := &UpsertWithDebug{}

	if c.debugMode {
		result.Debug = &UpsertDebugInfo{
			CollectionName: c.collectionName,
			NumPoints:      len(points),
			VectorSize:     c.vectorSize,
			RequestTime:    requestTime,
			ChunkSources:   chunkSources,
		}
	}

	// log.Printf("Successfully upserted %d points.", len(points))
	return result, nil
}

// Search performs a similarity search in the Qdrant collection
func (c *Client) Search(ctx context.Context, queryEmbedding []float32, limit uint64) ([]*qdrant.ScoredPoint, error) {
	result, err := c.SearchWithDebug(ctx, queryEmbedding, limit)
	if err != nil {
		return nil, err
	}
	return result.Results, nil
}

// SearchWithDebug performs a similarity search in the Qdrant collection with debug information
func (c *Client) SearchWithDebug(ctx context.Context, queryEmbedding []float32, limit uint64) (*SearchWithDebug, error) {
	startTime := time.Now()
	withPayload := true
	withVectors := false

	searchRequest := &qdrant.SearchPoints{
		CollectionName: c.collectionName,
		Vector:         queryEmbedding,
		Limit:          limit,
		WithPayload: &qdrant.WithPayloadSelector{
			SelectorOptions: &qdrant.WithPayloadSelector_Enable{
				Enable: withPayload,
			},
		},
		WithVectors: &qdrant.WithVectorsSelector{
			SelectorOptions: &qdrant.WithVectorsSelector_Enable{
				Enable: withVectors,
			},
		},
	}

	searchResult, err := c.qdrantClient.Search(ctx, searchRequest)
	if err != nil {
		return nil, fmt.Errorf("could not perform search: %w", err)
	}

	requestTime := time.Since(startTime)
	results := searchResult.GetResult()

	result := &SearchWithDebug{
		Results: results,
	}

	if c.debugMode {
		retrievedSources := make([]string, len(results))
		var topScore, lowestScore float32

		for i, scoredPoint := range results {
			if source := scoredPoint.Payload["source"]; source != nil {
				retrievedSources[i] = source.GetStringValue()
			}

			if i == 0 {
				topScore = scoredPoint.Score
				lowestScore = scoredPoint.Score
			} else {
				if scoredPoint.Score > topScore {
					topScore = scoredPoint.Score
				}
				if scoredPoint.Score < lowestScore {
					lowestScore = scoredPoint.Score
				}
			}
		}

		result.Debug = &SearchDebugInfo{
			CollectionName:   c.collectionName,
			QueryVectorSize:  len(queryEmbedding),
			Limit:            limit,
			RequestTime:      requestTime,
			ResultCount:      len(results),
			TopScore:         topScore,
			LowestScore:      lowestScore,
			RetrievedSources: retrievedSources,
		}
	}

	return result, nil
}
