// internal/rag/pipeline.go
package rag

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"

	"github.com/google/uuid"
	qdrantpb "github.com/qdrant/go-client/qdrant"
)

// RAGPipeline orchestrates the RAG process
type RAGPipeline struct {
	ollamaClient *ollama.Client
	qdrantClient *qdrant.Client
	chunkSize    int
	chunkOverlap int
	topK         uint64 // Number of documents to retrieve and use for context
	debugMode    bool
	// REMOVED: topN parameter
}

// NewRAGPipeline creates a new RAG pipeline
func NewRAGPipeline(ollamaClient *ollama.Client, qdrantClient *qdrant.Client, cfg *config.Config, chunkSize, chunkOverlap int, topK uint64) *RAGPipeline {
	return &RAGPipeline{
		ollamaClient: ollamaClient,
		qdrantClient: qdrantClient,
		chunkSize:    chunkSize,
		chunkOverlap: chunkOverlap,
		topK:         topK,
		debugMode:    cfg.DebugMode,
	}
}

// IngestDebugInfo contains debug information for document ingestion
type IngestDebugInfo struct {
	Source           string        `json:"source"`
	ContentLength    int           `json:"content_length"`
	ChunksCreated    int           `json:"chunks_created"`
	ChunkSize        int           `json:"chunk_size"`
	ChunkOverlap     int           `json:"chunk_overlap"`
	EmbeddingTime    time.Duration `json:"embedding_time"`
	StorageTime      time.Duration `json:"storage_time"`
	TotalTime        time.Duration `json:"total_time"`
}

// IngestWithDebug contains debug information for document ingestion
type IngestWithDebug struct {
	Debug *IngestDebugInfo `json:"debug,omitempty"`
}

// RAGQueryDebugInfo contains debug information for RAG queries
type RAGQueryDebugInfo struct {
	Query                string                     `json:"query"`
	QueryLength          int                        `json:"query_length"`
	EmbeddingTime        time.Duration              `json:"embedding_time"`
	RetrievalTime        time.Duration              `json:"retrieval_time"`
	GenerationTime       time.Duration              `json:"generation_time"`
	TotalTime            time.Duration              `json:"total_time"`
	TopK                 uint64                     `json:"top_k"`
	RetrievedDocsCount   int                        `json:"retrieved_docs_count"`
	RetrievedSources     []string                   `json:"retrieved_sources"`
	RetrievedScores      []float32                  `json:"retrieved_scores"`
	ContextLength        int                        `json:"context_length"`
	ResponseLength       int                        `json:"response_length"`
	EmbeddingDebug       *ollama.EmbeddingDebugInfo `json:"embedding_debug,omitempty"`
	SearchDebug          *qdrant.SearchDebugInfo    `json:"search_debug,omitempty"`
	GenerationDebug      *ollama.GenerationDebugInfo `json:"generation_debug,omitempty"`
}

// RAGQueryWithDebug contains both the RAG query result and debug information
type RAGQueryWithDebug struct {
	Answer string             `json:"answer"`
	Debug  *RAGQueryDebugInfo `json:"debug,omitempty"`
}

// --- IngestDocument and splitContent functions remain exactly the same ---
// IngestDocument splits a document, generates embeddings, and stores them
func (p *RAGPipeline) IngestDocument(ctx context.Context, content, source string) error {
	result, err := p.IngestDocumentWithDebug(ctx, content, source)
	if err != nil {
		return err
	}
	_ = result // Ignore debug result in non-debug mode
	return nil
}

// IngestDocumentWithDebug splits a document, generates embeddings, and stores them with debug information
func (p *RAGPipeline) IngestDocumentWithDebug(ctx context.Context, content, source string) (*IngestWithDebug, error) {
	startTime := time.Now()
	// log.Printf("Starting ingestion for source: %s", source)

	// 1. Document Splitting
	chunks := p.splitContent(content, source)
	if len(chunks) == 0 {
		return nil, fmt.Errorf("no chunks were created from the content of source: %s", source)
	}

	// log.Printf("Split content into %d chunks.", len(chunks))

	// 2. Embedding
	embeddingStartTime := time.Now()
	chunkContents := make([]string, len(chunks))
	for i, chunk := range chunks {
		chunkContents[i] = chunk.Content
	}

	embeddings := make([][]float32, len(chunks))
	for i, chunkContent := range chunkContents {
		embedding, err := p.ollamaClient.GetEmbedding(ctx, chunkContent)
		if err != nil {
			return nil, fmt.Errorf("failed to generate embedding for chunk %d: %w", i, err)
		}
		embeddings[i] = embedding
		// log.Printf("Generated embedding for chunk %d of %d", i+1, len(chunks))
	}
	embeddingTime := time.Since(embeddingStartTime)

	// 3. Storage
	storageStartTime := time.Now()
	err := p.qdrantClient.UpsertPoints(ctx, chunks, embeddings)
	if err != nil {
		return nil, fmt.Errorf("failed to upsert document chunks: %w", err)
	}
	storageTime := time.Since(storageStartTime)

	totalTime := time.Since(startTime)

	result := &IngestWithDebug{}

	if p.debugMode {
		result.Debug = &IngestDebugInfo{
			Source:        source,
			ContentLength: len(content),
			ChunksCreated: len(chunks),
			ChunkSize:     p.chunkSize,
			ChunkOverlap:  p.chunkOverlap,
			EmbeddingTime: embeddingTime,
			StorageTime:   storageTime,
			TotalTime:     totalTime,
		}
	}

	// log.Printf("Successfully ingested document from source: %s", source)
	return result, nil
}

// splitContent performs simple character-based splitting
func (p *RAGPipeline) splitContent(content, source string) []qdrant.DocumentChunk {
	var chunks []qdrant.DocumentChunk
	step := p.chunkSize - p.chunkOverlap
	for i := 0; i < len(content); i += step {
		end := i + p.chunkSize
		if end > len(content) {
			end = len(content)
		}
		chunkContent := content[i:end]
		if strings.TrimSpace(chunkContent) == "" {
			continue
		}
		chunks = append(chunks, qdrant.DocumentChunk{
			ID:      uuid.New().String(),
			Content: chunkContent,
			Source:  source,
		})
		if end == len(content) {
			break
		}
	}
	return chunks
}


// RAGQuery executes the full RAG process for a given query (SIMPLIFIED)
func (p *RAGPipeline) RAGQuery(ctx context.Context, query string) (string, error) {
	result, err := p.RAGQueryWithDebug(ctx, query)
	if err != nil {
		return "", err
	}
	return result.Answer, nil
}

// RAGQueryWithDebug executes the full RAG process for a given query with debug information
func (p *RAGPipeline) RAGQueryWithDebug(ctx context.Context, query string) (*RAGQueryWithDebug, error) {
	startTime := time.Now()
	// log.Printf("Executing simplified RAG query: '%s'", query)

	var embeddingDebug *ollama.EmbeddingDebugInfo
	var searchDebug *qdrant.SearchDebugInfo
	var generationDebug *ollama.GenerationDebugInfo

	// 1. Query Embedding
	// log.Println("Generating query embedding...")
	embeddingStartTime := time.Now()
	var queryEmbedding []float32
	var err error

	if p.debugMode {
		embeddingResult, embErr := p.ollamaClient.GetEmbeddingWithDebug(ctx, query)
		if embErr != nil {
			return nil, fmt.Errorf("failed to embed query: %w", embErr)
		}
		queryEmbedding = embeddingResult.Embedding
		embeddingDebug = embeddingResult.Debug
	} else {
		queryEmbedding, err = p.ollamaClient.GetEmbedding(ctx, query)
		if err != nil {
			return nil, fmt.Errorf("failed to embed query: %w", err)
		}
	}
	embeddingTime := time.Since(embeddingStartTime)

	// 2. Retrieval
	// log.Printf("Retrieving top %d documents...", p.topK)
	retrievalStartTime := time.Now()
	var retrievedDocs []*qdrantpb.ScoredPoint

	if p.debugMode {
		searchResult, searchErr := p.qdrantClient.SearchWithDebug(ctx, queryEmbedding, p.topK)
		if searchErr != nil {
			return nil, fmt.Errorf("failed to retrieve documents: %w", searchErr)
		}
		retrievedDocs = searchResult.Results
		searchDebug = searchResult.Debug
	} else {
		retrievedDocs, err = p.qdrantClient.Search(ctx, queryEmbedding, p.topK)
		if err != nil {
			return nil, fmt.Errorf("failed to retrieve documents: %w", err)
		}
	}
	retrievalTime := time.Since(retrievalStartTime)

	if len(retrievedDocs) == 0 {
		result := &RAGQueryWithDebug{
			Answer: "I could not find any relevant information in the knowledge base to answer your query.",
		}
		if p.debugMode {
			result.Debug = &RAGQueryDebugInfo{
				Query:              query,
				QueryLength:        len(query),
				EmbeddingTime:      embeddingTime,
				RetrievalTime:      retrievalTime,
				GenerationTime:     0,
				TotalTime:          time.Since(startTime),
				TopK:               p.topK,
				RetrievedDocsCount: 0,
				RetrievedSources:   []string{},
				RetrievedScores:    []float32{},
				ContextLength:      0,
				ResponseLength:     len(result.Answer),
				EmbeddingDebug:     embeddingDebug,
				SearchDebug:        searchDebug,
				GenerationDebug:    nil,
			}
		}
		return result, nil
	}
	// log.Printf("Retrieved %d documents.", len(retrievedDocs))

	// 3. Context Formulation (No Reranking)
	// log.Printf("Formulating context from top %d retrieved documents...", len(retrievedDocs))
	var contextBuilder strings.Builder
	retrievedSources := make([]string, len(retrievedDocs))
	retrievedScores := make([]float32, len(retrievedDocs))

	for i, doc := range retrievedDocs {
		content := doc.Payload["content"].GetStringValue()
		source := doc.Payload["source"].GetStringValue()
		retrievedSources[i] = source
		retrievedScores[i] = doc.Score
		contextBuilder.WriteString(fmt.Sprintf("Source: %s\nContent: %s\n\n", source, content))
	}
	contextText := contextBuilder.String()

	// 4. Generation
	// log.Println("Generating final answer...")
	generationStartTime := time.Now()
	var answer string

	if p.debugMode {
		generationResult, genErr := p.ollamaClient.GenerateCompletionWithDebug(ctx, contextText, query)
		if genErr != nil {
			return nil, fmt.Errorf("failed to generate completion: %w", genErr)
		}
		answer = generationResult.Response
		generationDebug = generationResult.Debug
	} else {
		answer, err = p.ollamaClient.GenerateCompletion(ctx, contextText, query)
		if err != nil {
			return nil, fmt.Errorf("failed to generate completion: %w", err)
		}
	}
	generationTime := time.Since(generationStartTime)

	totalTime := time.Since(startTime)

	result := &RAGQueryWithDebug{
		Answer: answer,
	}

	if p.debugMode {
		result.Debug = &RAGQueryDebugInfo{
			Query:              query,
			QueryLength:        len(query),
			EmbeddingTime:      embeddingTime,
			RetrievalTime:      retrievalTime,
			GenerationTime:     generationTime,
			TotalTime:          totalTime,
			TopK:               p.topK,
			RetrievedDocsCount: len(retrievedDocs),
			RetrievedSources:   retrievedSources,
			RetrievedScores:    retrievedScores,
			ContextLength:      len(contextText),
			ResponseLength:     len(answer),
			EmbeddingDebug:     embeddingDebug,
			SearchDebug:        searchDebug,
			GenerationDebug:    generationDebug,
		}
	}

	return result, nil
}
