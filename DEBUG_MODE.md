# Debug Mode Documentation

This document describes the debug mode functionality added to all internal packages in the RAG pipeline project.

## Overview

Debug mode provides detailed information about the internal operations of the RAG pipeline, including:
- Embedding generation details
- Vector search operations
- Document ingestion statistics
- Query processing metrics
- Referenced documents and sources

## Configuration

Debug mode is controlled by the `DEBUG_MODE` environment variable:

```bash
# Enable debug mode
export DEBUG_MODE=true

# Disable debug mode (default)
export DEBUG_MODE=false
```

## Debug Information by Package

### 1. Config Package

The config package now includes a `DebugMode` field that controls debug functionality across all packages.

### 2. Ollama Package

#### Embedding Debug Info
- Model used for embedding
- Input text and length
- Request processing time
- Embedding dimension
- API endpoint used

#### Generation Debug Info
- Model used for generation
- Context text and query
- Full prompt sent to model
- Prompt and response lengths
- Request processing time
- API endpoint used

### 3. Qdrant Package

#### Search Debug Info
- Collection name
- Query vector size
- Search limit
- Request processing time
- Number of results returned
- Score range (highest and lowest)
- Retrieved document sources

#### Upsert Debug Info
- Collection name
- Number of points upserted
- Vector size
- Request processing time
- Source files of chunks

### 4. RAG Package

#### Document Ingestion Debug Info
- Source document path
- Content length
- Number of chunks created
- Chunk size and overlap settings
- Time spent on embedding generation
- Time spent on storage
- Total processing time

#### RAG Query Debug Info
- Query text and length
- Time breakdown (embedding, retrieval, generation)
- Total processing time
- Number of documents retrieved
- Retrieved document sources and scores
- Context length used for generation
- Response length
- Detailed debug info from underlying operations

## Usage Examples

### Basic Usage with Environment Variable

```bash
# Run with debug mode enabled
DEBUG_MODE=true go run main.go

# Run with debug mode disabled
DEBUG_MODE=false go run main.go
```

### Programmatic Usage

```go
// Enable debug mode in config
cfg := &config.Config{
    // ... other config fields ...
    DebugMode: true,
}

// Use debug methods directly
result, err := ragPipeline.RAGQueryWithDebug(ctx, "What is RAG?")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Answer: %s\n", result.Answer)
if result.Debug != nil {
    // Access detailed debug information
    fmt.Printf("Query processed in: %v\n", result.Debug.TotalTime)
    fmt.Printf("Retrieved %d documents\n", result.Debug.RetrievedDocsCount)
    fmt.Printf("Sources: %v\n", result.Debug.RetrievedSources)
}
```

### Debug Methods Available

Each package provides both regular and debug versions of methods:

#### Ollama Package
- `GetEmbedding()` / `GetEmbeddingWithDebug()`
- `GenerateCompletion()` / `GenerateCompletionWithDebug()`

#### Qdrant Package
- `Search()` / `SearchWithDebug()`
- `UpsertPoints()` / `UpsertPointsWithDebug()`

#### RAG Package
- `IngestDocument()` / `IngestDocumentWithDebug()`
- `RAGQuery()` / `RAGQueryWithDebug()`

## Debug Output Format

Debug information is returned as structured data that can be easily serialized to JSON:

```json
{
  "query": "What is RAG?",
  "query_length": 12,
  "embedding_time": "150ms",
  "retrieval_time": "45ms",
  "generation_time": "2.3s",
  "total_time": "2.495s",
  "top_k": 5,
  "retrieved_docs_count": 3,
  "retrieved_sources": [
    "./docs/rag_overview.md",
    "./docs/qwen_info.md"
  ],
  "retrieved_scores": [0.85, 0.72],
  "context_length": 1024,
  "response_length": 156,
  "embedding_debug": {
    "model": "qwen-embedding",
    "input_length": 12,
    "embedding_dimension": 1024,
    "request_time": "150ms"
  },
  "search_debug": {
    "collection_name": "rag_pipeline_go",
    "query_vector_size": 1024,
    "limit": 5,
    "result_count": 3,
    "top_score": 0.85,
    "lowest_score": 0.72
  },
  "generation_debug": {
    "model": "qwen3:1.7b",
    "prompt_length": 1200,
    "response_length": 156,
    "request_time": "2.3s"
  }
}
```

## Testing Debug Mode

A test file `test_debug.go` is provided to demonstrate debug functionality:

```bash
go run test_debug.go
```

This will run tests for both debug and normal modes, showing the difference in output.

## Performance Considerations

- Debug mode adds minimal overhead when disabled
- When enabled, debug mode collects timing and metadata information
- Debug information is only computed when explicitly requested
- No performance impact on production deployments when debug mode is disabled

## Integration with Main Application

The main application automatically detects debug mode from the configuration and:
- Uses debug methods when debug mode is enabled
- Displays formatted debug information
- Falls back to regular methods when debug mode is disabled

This ensures backward compatibility while providing rich debugging capabilities when needed.
