// test.go - Simple test runner for RAG pipeline performance evaluation
package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/denissud/rag-pipeline-go/config"
	"github.com/denissud/rag-pipeline-go/internal/ollama"
	"github.com/denissud/rag-pipeline-go/internal/qdrant"
	"github.com/denissud/rag-pipeline-go/internal/rag"
)

type TestResult struct {
	Query            string
	Answer           string
	Debug            *rag.RAGQueryDebugInfo
	ReferencedDocs   map[string]string
	Timestamp        time.Time
}

func loadDocuments(docsDir string) (map[string]string, error) {
	documents := make(map[string]string)
	
	files, err := os.ReadDir(docsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %w", docsDir, err)
	}

	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".md" {
			filePath := filepath.Join(docsDir, file.Name())
			content, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("Failed to read file %s: %v", filePath, err)
				continue
			}
			documents[filePath] = string(content)
		}
	}
	
	return documents, nil
}

func generateSimpleReport(results []TestResult, documents map[string]string, outputFile string) error {
	var report strings.Builder

	report.WriteString("# RAG Pipeline Test Results\n\n")
	report.WriteString(fmt.Sprintf("**Generated:** %s\n\n", time.Now().Format("2006-01-02 15:04:05")))
	report.WriteString(fmt.Sprintf("**Total Queries:** %d\n\n", len(results)))

	for i, result := range results {
		report.WriteString(fmt.Sprintf("## Query %d\n\n", i+1))
		report.WriteString(fmt.Sprintf("**Question:** %s\n\n", result.Query))
		report.WriteString(fmt.Sprintf("**Answer:** %s\n\n", result.Answer))

		if result.Debug != nil {
			report.WriteString("**Performance:**\n")
			report.WriteString(fmt.Sprintf("- Total time: %v\n", result.Debug.TotalTime))
			report.WriteString(fmt.Sprintf("- Documents retrieved: %d\n", result.Debug.RetrievedDocsCount))
			
			if len(result.Debug.RetrievedSources) > 0 {
				report.WriteString("- Sources: ")
				for j, source := range result.Debug.RetrievedSources {
					if j > 0 {
						report.WriteString(", ")
					}
					report.WriteString(fmt.Sprintf("`%s`", filepath.Base(source)))
				}
				report.WriteString("\n")
			}
			report.WriteString("\n")
		}

		// Show referenced content
		if len(result.ReferencedDocs) > 0 {
			report.WriteString("**Referenced Content:**\n\n")
			for source, content := range result.ReferencedDocs {
				report.WriteString(fmt.Sprintf("### %s\n\n", filepath.Base(source)))
				// Show first 500 characters
				if len(content) > 500 {
					report.WriteString(content[:500] + "...\n\n")
				} else {
					report.WriteString(content + "\n\n")
				}
			}
		}

		report.WriteString("---\n\n")
	}

	return os.WriteFile(outputFile, []byte(report.String()), 0644)
}

func main() {
	fmt.Println("=== RAG Pipeline Test ===")
	
	// Load configuration
	cfg := config.Load()
	cfg.DebugMode = true // Force debug mode for testing
	
	// Test queries
	testQueries := []string{
		"What is a RAG pipeline?",
		"What is Qwen3-Embedding-0.6B?",
		"How do you use Go with Nix?",
		"What are the benefits of using Go?",
		"What is Qdrant?",
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Load documents
	fmt.Println("Loading documents...")
	documents, err := loadDocuments("./docs")
	if err != nil {
		log.Fatalf("Failed to load documents: %v", err)
	}
	fmt.Printf("Loaded %d documents\n", len(documents))

	// Initialize clients
	fmt.Println("Initializing RAG pipeline...")
	ollamaClient := ollama.NewClient(cfg)
	qdrantClient, err := qdrant.NewClient(ctx, cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Qdrant client: %v", err)
	}

	ragPipeline := rag.NewRAGPipeline(
		ollamaClient,
		qdrantClient,
		cfg,
		1024, // Chunk Size
		128,  // Chunk Overlap
		3,    // Top K for retrieval
	)

	// Ingest documents
	fmt.Println("Ingesting documents...")
	for source, content := range documents {
		err := ragPipeline.IngestDocument(ctx, content, source)
		if err != nil {
			log.Printf("Failed to ingest %s: %v", source, err)
		}
	}

	// Run test queries
	fmt.Println("\nRunning test queries...")
	var results []TestResult

	for i, query := range testQueries {
		fmt.Printf("[%d/%d] %s\n", i+1, len(testQueries), query)

		queryCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
		result, err := ragPipeline.RAGQueryWithDebug(queryCtx, query)
		cancel()

		if err != nil {
			log.Printf("Error: %v", err)
			continue
		}

		// Extract referenced documents
		referencedDocs := make(map[string]string)
		if result.Debug != nil {
			for _, source := range result.Debug.RetrievedSources {
				if content, exists := documents[source]; exists {
					referencedDocs[source] = content
				}
			}
		}

		testResult := TestResult{
			Query:          query,
			Answer:         result.Answer,
			Debug:          result.Debug,
			ReferencedDocs: referencedDocs,
			Timestamp:      time.Now(),
		}

		results = append(results, testResult)
		
		fmt.Printf("Answer: %s\n", result.Answer)
		if result.Debug != nil {
			fmt.Printf("Time: %v, Docs: %d\n", result.Debug.TotalTime, result.Debug.RetrievedDocsCount)
		}
		fmt.Println()
	}

	// Generate report
	outputFile := fmt.Sprintf("test_results_%s.md", time.Now().Format("2006-01-02_15-04-05"))
	fmt.Printf("Generating report: %s\n", outputFile)
	
	err = generateSimpleReport(results, documents, outputFile)
	if err != nil {
		log.Fatalf("Failed to generate report: %v", err)
	}

	fmt.Printf("\n=== Test Complete ===\n")
	fmt.Printf("Report saved to: %s\n", outputFile)
	fmt.Printf("Processed %d queries\n", len(results))
}
